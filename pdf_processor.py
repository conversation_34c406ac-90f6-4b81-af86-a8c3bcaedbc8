#!/usr/bin/env python3
"""
PDF批量处理脚本
使用PPStructureV3模型处理PDF文件，输出JSON和Markdown格式
支持表格识别和文档方向分类
"""

import argparse
import os
import sys
from pathlib import Path
from typing import List, Dict, Any
import json
from tqdm import tqdm
from paddleocr import PPStructureV3


def setup_pipeline():
    """初始化PPStructureV3管道，启用表格识别和文档方向分类"""
    pipeline = PPStructureV3(
        use_doc_orientation_classify=True,  # 启用文档方向分类
        use_doc_unwarping=False,
        use_table_recognition=True,         # 启用表格识别
    )
    return pipeline


def get_pdf_files(input_dir: Path) -> List[Path]:
    """获取输入目录下的所有PDF文件"""
    pdf_files = []
    for file_path in input_dir.rglob("*.pdf"):
        if file_path.is_file():
            pdf_files.append(file_path)
    return sorted(pdf_files)


def process_single_pdf(pipeline, pdf_path: Path, output_dir: Path) -> Dict[str, Any]:
    """
    处理单个PDF文件
    
    Args:
        pipeline: PPStructureV3实例
        pdf_path: PDF文件路径
        output_dir: 输出目录
        
    Returns:
        处理结果统计信息
    """
    pdf_name = pdf_path.stem
    
    # 创建输出子目录
    pdf_output_dir = output_dir / pdf_name
    pdf_output_dir.mkdir(parents=True, exist_ok=True)
    
    # 存储所有页面的markdown信息，用于合并
    all_markdown_pages = []
    page_count = 0
    
    try:
        # 使用predict_iter处理PDF，适合大文件
        results_iter = pipeline.predict_iter(
            input=str(pdf_path),
            use_doc_orientation_classify=True,
            use_table_recognition=True,
            use_table_orientation_classify=True,
        )
        
        # 处理每一页
        for res in results_iter:
            page_count += 1
            page_index = res["page_index"]
            
            # 保存JSON格式
            json_filename = f"{pdf_name}_page_{page_index:03d}.json"
            json_path = pdf_output_dir / json_filename
            res.save_to_json(save_path=str(pdf_output_dir))
            
            # 获取markdown信息用于后续合并
            markdown_info = res._to_markdown(pretty=True)
            all_markdown_pages.append(markdown_info)
            
            # 保存单页markdown（可选，用于调试）
            markdown_filename = f"{pdf_name}_page_{page_index:03d}.md"
            markdown_path = pdf_output_dir / markdown_filename
            res.save_to_markdown(save_path=str(pdf_output_dir))
        
        # 合并所有页面的markdown
        if all_markdown_pages:
            merged_markdown = pipeline.concatenate_markdown_pages(all_markdown_pages)
            
            # 保存合并后的markdown文件
            merged_markdown_path = pdf_output_dir / f"{pdf_name}_merged.md"
            with open(merged_markdown_path, 'w', encoding='utf-8') as f:
                f.write(merged_markdown["markdown_texts"])
            
            # 保存合并后的图片
            if "markdown_images" in merged_markdown:
                images_dir = pdf_output_dir / "images"
                images_dir.mkdir(exist_ok=True)
                for img_path, img_obj in merged_markdown["markdown_images"].items():
                    img_save_path = images_dir / Path(img_path).name
                    img_obj.save(str(img_save_path))
        
        return {
            "status": "success",
            "pages_processed": page_count,
            "output_dir": str(pdf_output_dir)
        }
        
    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "pages_processed": page_count,
            "output_dir": str(pdf_output_dir)
        }


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="批量处理PDF文件，输出JSON和Markdown格式",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python pdf_processor.py --input input --output output
  python pdf_processor.py -i ./pdfs -o ./results
        """
    )
    
    parser.add_argument(
        "--input", "-i",
        type=str,
        default="input",
        help="输入目录路径 (默认: input)"
    )
    
    parser.add_argument(
        "--output", "-o", 
        type=str,
        default="output",
        help="输出目录路径 (默认: output)"
    )
    
    args = parser.parse_args()
    
    # 验证输入目录
    input_dir = Path(args.input)
    if not input_dir.exists():
        print(f"错误: 输入目录 '{input_dir}' 不存在")
        sys.exit(1)
    
    if not input_dir.is_dir():
        print(f"错误: '{input_dir}' 不是一个目录")
        sys.exit(1)
    
    # 创建输出目录
    output_dir = Path(args.output)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 获取所有PDF文件
    pdf_files = get_pdf_files(input_dir)
    
    if not pdf_files:
        print(f"在目录 '{input_dir}' 中没有找到PDF文件")
        sys.exit(0)
    
    print(f"找到 {len(pdf_files)} 个PDF文件")
    print(f"输入目录: {input_dir}")
    print(f"输出目录: {output_dir}")
    print("正在初始化模型...")
    
    # 初始化管道
    pipeline = setup_pipeline()
    print("模型初始化完成")
    
    # 处理结果统计
    results_summary = []
    
    # 使用进度条处理每个PDF
    with tqdm(pdf_files, desc="处理PDF文件", unit="文件") as pbar:
        for pdf_path in pbar:
            pbar.set_description(f"处理: {pdf_path.name}")
            
            result = process_single_pdf(pipeline, pdf_path, output_dir)
            result["pdf_name"] = pdf_path.name
            results_summary.append(result)
            
            # 更新进度条显示
            if result["status"] == "success":
                pbar.set_postfix(
                    页数=result["pages_processed"],
                    状态="成功"
                )
            else:
                pbar.set_postfix(
                    页数=result["pages_processed"],
                    状态="失败"
                )
    
    # 输出处理结果摘要
    print("\n" + "="*60)
    print("处理完成摘要:")
    print("="*60)
    
    successful_count = 0
    failed_count = 0
    total_pages = 0
    
    for result in results_summary:
        status_icon = "✓" if result["status"] == "success" else "✗"
        print(f"{status_icon} {result['pdf_name']}: {result['pages_processed']} 页")
        
        if result["status"] == "success":
            successful_count += 1
            total_pages += result["pages_processed"]
        else:
            failed_count += 1
            print(f"   错误: {result['error']}")
    
    print("-"*60)
    print(f"成功处理: {successful_count} 个文件")
    print(f"处理失败: {failed_count} 个文件") 
    print(f"总页数: {total_pages} 页")
    print(f"输出目录: {output_dir}")
    
    # 保存处理结果摘要
    summary_path = output_dir / "processing_summary.json"
    with open(summary_path, 'w', encoding='utf-8') as f:
        json.dump(results_summary, f, ensure_ascii=False, indent=2)
    
    print(f"处理摘要已保存到: {summary_path}")


if __name__ == "__main__":
    main()
